# 爱宠看世界 - 底部标签栏图标更换指南

## 概述

本指南将帮助您将小程序底部标签栏的图标更换为新设计的更美观的图标。新的图标设计包括首页和个人中心两个标签，每个标签各有默认状态和激活状态的图标。

## 新图标预览

### 首页图标
- 默认状态：简约房子形状图标（灰色）
- 激活状态：简约房子形状图标（主题色 #FF8A5B）

### 个人中心图标
- 默认状态：简约人像轮廓图标（灰色）
- 激活状态：简约人像轮廓图标（主题色 #FF8A5B）

## 更换步骤

1. **准备新图标**
   - 打开 `icon-converter.html` 文件
   - 使用工具将SVG图标转换为PNG格式
   - 确保生成4个图标文件：`home.png`, `home-active.png`, `profile.png`, `profile-active.png`

2. **替换图标文件**
   - 将生成的4个PNG文件放入项目的 `images/icons` 目录
   - 替换原有的同名图标文件

3. **确认配置正确**
   - 打开 `app.json` 文件
   - 确认 tabBar 配置中使用了正确的图标路径：
   ```json
   "tabBar": {
     "color": "#999999",
     "selectedColor": "#FF8A5B",
     "backgroundColor": "#ffffff",
     "borderStyle": "black",
     "list": [
       {
         "pagePath": "pages/index/index",
         "text": "首页",
         "iconPath": "images/icons/home.png",
         "selectedIconPath": "images/icons/home-active.png"
       },
       {
         "pagePath": "pages/profile/profile",
         "text": "个人中心",
         "iconPath": "images/icons/profile.png",
         "selectedIconPath": "images/icons/profile-active.png"
       }
     ]
   }
   ```

4. **编译和预览**
   - 重新编译小程序
   - 在模拟器中预览，检查底部标签栏图标是否已更新
   - 确认默认状态和激活状态的图标显示正确

## 图标设计说明

1. **首页图标**
   - 设计理念：简洁清晰的房子形状，包含窗户和门的细节，直观表达"首页"的含义
   - 线条使用圆角处理，风格现代温和
   - 激活状态使用主题色 #FF8A5B，与整体主题色调协调

2. **个人中心图标**
   - 设计理念：现代简约的人像轮廓，包含头部和身体形状，直观表达"个人中心"的含义
   - 使用简洁的圆形和曲线，形成友好的视觉效果
   - 激活状态同样使用主题色 #FF8A5B，保持统一风格

## 注意事项

- 图标尺寸为 64x64 像素，适合微信小程序标签栏使用
- 如果需要修改图标设计，可以编辑源SVG文件然后重新生成PNG
- 确保图标文件名称与 app.json 中的配置保持一致 