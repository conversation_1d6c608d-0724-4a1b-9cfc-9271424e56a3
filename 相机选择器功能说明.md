# 相机选择器功能说明

## 功能概述

将原来分离的"切换相机"按钮和"CAM"按钮整合为统一的相机选择器，用户可以在手机前置相机、手机后置相机和硬件CAM相机之间无缝切换。

## 主要功能

### 1. 统一的相机选择界面
- **单一入口**：将原来的两个按钮合并为一个"相机选择"按钮
- **直观显示**：按钮图标和文字根据当前选择的相机类型动态变化
- **状态指示**：清晰显示当前使用的相机设备

### 2. 三种相机模式支持
- **手机后置相机** 📷
  - 默认选择
  - 支持完整的视觉处理功能（二色视觉、夜视、运动视觉等）
  - 标准分辨率设置

- **手机前置相机** 🤳  
  - 自拍模式
  - 支持完整的视觉处理功能
  - 适合近距离观察

- **硬件CAM相机** 📡
  - 外接硬件设备
  - 暂时不支持图像处理功能，显示原始图像
  - 特殊状态指示

### 3. 智能切换逻辑
- **资源管理**：切换时自动释放前一相机的资源
- **状态同步**：确保UI显示与实际相机状态保持一致
- **错误处理**：网络异常或设备问题时的自动回退机制

## 技术实现

### 数据结构
```javascript
// 相机选择器相关状态
showCameraSelector: false,        // 选择器显示状态
currentCameraType: 'back',        // 当前相机类型
cameraOptions: [                  // 可选相机列表
  { type: 'back', name: '后置相机', icon: '📷', desc: '手机后置摄像头' },
  { type: 'front', name: '前置相机', icon: '🤳', desc: '手机前置摄像头' },
  { type: 'cam', name: '硬件相机', icon: '📡', desc: 'CAM硬件摄像设备' }
]
```

### 核心方法
- `toggleCameraSelector()` - 显示/隐藏相机选择器
- `selectCameraType()` - 选择特定相机类型
- `switchToCamera()` - 执行相机切换逻辑
- `switchToPhoneCamera()` - 切换到手机相机
- `switchToHardwareCamera()` - 切换到硬件相机

### 视觉处理差异化
```javascript
// 在processFrameWebGL函数中的处理逻辑
if (this.data.camMode) {
  console.log('CAM模式下不进行WebGL视觉处理');
  return; // 硬件相机模式直接跳过图像处理
}
// 手机相机继续进行完整的视觉处理...
```

## 用户体验优化

### 1. 视觉反馈
- **动态图标**：按钮图标根据当前相机类型变化
- **触觉反馈**：选择时提供轻微震动
- **状态提示**：选择面板中显示当前选中状态
- **连接指示**：硬件相机显示连接状态

### 2. 交互设计
- **遮罩层**：点击外部区域关闭选择器
- **滑动动画**：选择面板的进入/退出动画
- **防误触**：合理的点击区域设计
- **一键切换**：保持原有的快速切换体验

### 3. 错误处理
- **自动回退**：硬件相机连接失败时自动返回手机相机
- **状态重置**：异常情况下的状态恢复机制
- **用户提示**：清晰的错误信息和操作指导

## 兼容性说明

### 保留的功能
- `toggleCamMode()` 方法保留，现在会打开相机选择器
- 所有原有的CAM管理逻辑完全保留
- 手机相机的所有视觉处理功能不变

### 新增的功能
- 统一的相机选择界面
- 硬件相机模式的视觉提示
- 更好的状态同步机制

## 使用方法

1. **打开相机选择器**：点击底部的相机按钮
2. **选择相机类型**：从弹出的选择面板中选择需要的相机
3. **确认切换**：点击选项后自动执行切换
4. **状态确认**：观察按钮图标和文字的变化确认切换成功

## 注意事项

- 硬件相机模式下暂时没有视觉处理功能，显示原始图像
- 切换过程中可能有短暂的加载时间
- 确保硬件设备连接正常后再切换到CAM模式
- 如果硬件相机连接失败，系统会自动回退到手机后置相机 